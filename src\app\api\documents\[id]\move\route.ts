import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// PUT /api/documents/[id]/move
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { folderId } = body;
    
    console.log('API: Moving document:', id, 'to folder:', folderId);
    
    // Check if document exists
    const existingDocument = await prisma.document.findUnique({
      where: { id }
    });

    if (!existingDocument) {
      return NextResponse.json({ data: null, error: 'Document not found' }, { status: 404 });
    }

    // If folderId is provided, check if folder exists and belongs to the same project
    if (folderId) {
      const folder = await prisma.folder.findUnique({
        where: { id: folderId }
      });
      
      if (!folder) {
        return NextResponse.json({ data: null, error: 'Folder not found' }, { status: 404 });
      }
      
      if (folder.project_id !== existingDocument.project_id) {
        return NextResponse.json({ data: null, error: 'Folder belongs to different project' }, { status: 400 });
      }
      
      if (folder.type !== 'documents') {
        return NextResponse.json({ data: null, error: 'Folder is not a documents folder' }, { status: 400 });
      }
    }

    // Update the document's folder
    const document = await prisma.document.update({
      where: { id },
      data: { folder_id: folderId || null },
    });

    const documentData = {
      id: document.id,
      name: document.name,
      file_path: document.file_path,
      file_size: document.file_size,
      mime_type: document.mime_type,
      folder_id: document.folder_id,
      project_id: document.project_id,
      created_at: document.created_at.toISOString(),
      updated_at: document.updated_at.toISOString(),
    };

    return NextResponse.json({ data: documentData, error: null });
  } catch (error) {
    console.error('API Error in PUT /api/documents/[id]/move:', error);
    return NextResponse.json({ 
      data: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}
