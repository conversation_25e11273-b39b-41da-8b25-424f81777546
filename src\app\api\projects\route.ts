import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/projects
export async function GET() {
  try {
    console.log('API: Fetching projects from database');
    
    const projects = await prisma.project.findMany({
      orderBy: {
        created_at: 'desc',
      },
    });

    const projectsData = projects.map((project: any) => ({
      ...project,
      created_at: project.created_at.toISOString(),
      updated_at: project.updated_at.toISOString(),
    }));

    return NextResponse.json({ data: projectsData, error: null });
  } catch (error) {
    console.error('API Error in GET /api/projects:', error);
    return NextResponse.json({
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST /api/projects
export async function POST(request: NextRequest) {
  try {
    const project = await request.json();
    console.log('API: Creating project:', project);
    
    const createdProject = await prisma.project.create({
      data: {
        name: project.name,
        location: project.location || null,
        type: project.type || null,
        status: project.status || 'Planning',
        completion: project.completion || null,
        description: project.description || null,
        building_consent: project.building_consent || null,
        resource_consent: project.resource_consent || null,
        topo_start: project.topo_start || null,
        topo_completed: project.topo_completed || null,
        epa: project.epa || null,
        works_over: project.works_over || null,
        works_over_number: project.works_over_number || null,
        start_date: project.start_date || null,
        completion_date: project.completion_date || null,
        estimated_budget: project.estimated_budget || null,
        actual_cost: project.actual_cost || null,
        sale_price: project.sale_price || null,
        lender: project.lender || null,
        existing_dwellings: project.existing_dwellings || null,
        new_dwellings: project.new_dwellings || null,
        client_name: project.client_name || null,
        project_manager: project.project_manager || null,
      },
    });

    // Create default folders for the project
    try {
      await prisma.folder.createMany({
        data: [
          {
            name: 'All Documents',
            project_id: createdProject.id,
            type: 'documents',
            parent_id: null,
          },
          {
            name: 'All Photos',
            project_id: createdProject.id,
            type: 'photos',
            parent_id: null,
          },
        ],
      });
      console.log('Created default folders for project:', createdProject.id);
    } catch (folderError) {
      console.error('Error creating default folders:', folderError);
      // Don't fail the project creation if folder creation fails
    }

    const projectData = {
      ...createdProject,
      created_at: createdProject.created_at.toISOString(),
      updated_at: createdProject.updated_at.toISOString(),
    };

    return NextResponse.json({ data: projectData, error: null });
  } catch (error) {
    console.error('API Error in POST /api/projects:', error);
    return NextResponse.json({
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
