// Client-side service that calls API routes

export interface Photo {
  id: string;
  title: string;
  date: string;
  thumbnail: string;
  file_path?: string;
  project_id?: string;
  folder_id?: string;
}

/**
 * Get photos for a specific project or all photos
 */
export async function getPhotos(projectId?: string): Promise<{ data: Photo[] | null; error: any }> {
  try {
    console.log('Fetching photos from API', projectId ? `for project ${projectId}` : '');

    const url = projectId ? `/api/photos?projectId=${projectId}` : '/api/photos';
    const response = await fetch(url);
    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to fetch photos' };
    }

    return result;
  } catch (error) {
    console.error('Error in getPhotos:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Download a photo
 */
export async function downloadPhoto(id: string, filename: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Downloading photo:', id);

    const response = await fetch(`/api/photos/${id}/download`);

    if (!response.ok) {
      const result = await response.json();
      return { success: false, error: result.error || 'Failed to download photo' };
    }

    // Create a blob from the response
    const blob = await response.blob();

    // Create a temporary URL for the blob
    const url = window.URL.createObjectURL(blob);

    // Create a temporary anchor element and trigger download
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // Clean up
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in downloadPhoto:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Move a photo to a different folder
 */
export async function movePhoto(id: string, folderId: string | null): Promise<{ data: Photo | null; error: any }> {
  try {
    console.log('Moving photo:', id, 'to folder:', folderId);

    const response = await fetch(`/api/photos/${id}/move`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ folderId }),
    });

    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to move photo' };
    }

    return result;
  } catch (error) {
    console.error('Error in movePhoto:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Delete a photo
 */
export async function deletePhoto(id: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Deleting photo:', id);

    const response = await fetch(`/api/photos/${id}`, {
      method: 'DELETE',
    });

    const result = await response.json();

    if (!response.ok) {
      return { success: false, error: result.error || 'Failed to delete photo' };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in deletePhoto:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Network error' };
  }
}


