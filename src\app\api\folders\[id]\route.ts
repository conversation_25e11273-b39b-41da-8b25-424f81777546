import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// DELETE /api/folders/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log('API: Deleting folder:', id);
    
    // Check if folder exists
    const folder = await prisma.folder.findUnique({
      where: { id },
      include: {
        children: true,
        documents: true,
        photos: true,
      }
    });

    if (!folder) {
      return NextResponse.json({ data: null, error: 'Folder not found' }, { status: 404 });
    }

    // Check if folder has children
    if (folder.children.length > 0) {
      return NextResponse.json({ 
        data: null, 
        error: 'Cannot delete folder with subfolders. Please delete subfolders first.' 
      }, { status: 400 });
    }

    // Check if folder has files
    if (folder.documents.length > 0 || folder.photos.length > 0) {
      return NextResponse.json({ 
        data: null, 
        error: 'Cannot delete folder with files. Please move or delete files first.' 
      }, { status: 400 });
    }

    // Delete the folder
    await prisma.folder.delete({
      where: { id },
    });

    return NextResponse.json({ success: true, error: null });
  } catch (error) {
    console.error('API Error in DELETE /api/folders/[id]:', error);
    return NextResponse.json({ success: false, error: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}

// PUT /api/folders/[id] - for renaming folders
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { name } = body;
    
    if (!name) {
      return NextResponse.json({ data: null, error: 'Name is required' }, { status: 400 });
    }

    console.log('API: Renaming folder:', id, 'to:', name);
    
    // Check if folder exists
    const existingFolder = await prisma.folder.findUnique({
      where: { id }
    });

    if (!existingFolder) {
      return NextResponse.json({ data: null, error: 'Folder not found' }, { status: 404 });
    }

    // Update the folder
    const folder = await prisma.folder.update({
      where: { id },
      data: { name },
    });

    const folderData = {
      id: folder.id,
      name: folder.name,
      type: 'folder' as const,
      parentId: folder.parent_id,
      project_id: folder.project_id,
      created_at: folder.created_at.toISOString(),
      updated_at: folder.updated_at.toISOString(),
    };

    return NextResponse.json({ data: folderData, error: null });
  } catch (error) {
    console.error('API Error in PUT /api/folders/[id]:', error);
    return NextResponse.json({ data: null, error: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}
