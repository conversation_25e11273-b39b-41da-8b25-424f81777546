// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Project {
  id                  String   @id @default(cuid())
  name                String
  location            String?
  type                String?
  status              String   @default("Planning")
  completion          String?
  description         String?
  building_consent    String?
  resource_consent    String?
  topo_start          String?
  topo_completed      String?
  epa                 String?
  works_over          String?
  works_over_number   String?
  start_date          String?
  completion_date     String?
  estimated_budget    String?
  actual_cost         String?
  sale_price          String?
  lender              String?
  existing_dwellings  String?
  new_dwellings       String?
  client_name         String?
  project_manager     String?
  created_at          DateTime @default(now())
  updated_at          DateTime @updatedAt

  // Relations
  documents Document[]
  photos    Photo[]
  folders   Folder[]

  @@map("projects")
}

model Document {
  id          String   @id @default(cuid())
  project_id  String
  name        String
  file_path   String
  file_size   Int?
  mime_type   String?
  folder_id   String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  project Project @relation(fields: [project_id], references: [id], onDelete: Cascade)
  folder  Folder? @relation(fields: [folder_id], references: [id], onDelete: SetNull)

  @@map("documents")
}

model Photo {
  id          String   @id @default(cuid())
  project_id  String
  title       String
  file_path   String
  file_size   Int?
  mime_type   String?
  folder_id   String?
  thumbnail   String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  project Project @relation(fields: [project_id], references: [id], onDelete: Cascade)
  folder  Folder? @relation(fields: [folder_id], references: [id], onDelete: SetNull)

  @@map("photos")
}

model Folder {
  id          String   @id @default(cuid())
  name        String
  project_id  String?
  parent_id   String?
  type        String   // 'documents' or 'photos'
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  project     Project?    @relation(fields: [project_id], references: [id], onDelete: Cascade)
  parent      Folder?     @relation("FolderHierarchy", fields: [parent_id], references: [id], onDelete: Cascade)
  children    Folder[]    @relation("FolderHierarchy")
  documents   Document[]
  photos      Photo[]

  @@map("folders")
}
