import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const { auth_user_id } = await request.json()

    if (!auth_user_id) {
      return NextResponse.json(
        { error: 'Auth user ID is required' },
        { status: 400 }
      )
    }

    const user = await prisma.user.findUnique({
      where: {
        auth_user_id: auth_user_id
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      data: user,
      error: null
    })

  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
