// Client-side service that calls API routes

export interface Document {
  id: string;
  name: string;
  file_path: string;
  file_size?: number;
  mime_type?: string;
  folder_id?: string;
  project_id: string;
  created_at: string;
  updated_at: string;
}

/**
 * Get documents for a specific project
 */
export async function getDocuments(projectId: string): Promise<{ data: Document[] | null; error: any }> {
  try {
    console.log('Fetching documents for project:', projectId);

    const response = await fetch(`/api/documents?projectId=${projectId}`);
    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to fetch documents' };
    }

    return result;
  } catch (error) {
    console.error('Error in getDocuments:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Download a document
 */
export async function downloadDocument(id: string, filename: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Downloading document:', id);

    const response = await fetch(`/api/documents/${id}/download`);

    if (!response.ok) {
      const result = await response.json();
      return { success: false, error: result.error || 'Failed to download document' };
    }

    // Create a blob from the response
    const blob = await response.blob();

    // Create a temporary URL for the blob
    const url = window.URL.createObjectURL(blob);

    // Create a temporary anchor element and trigger download
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // Clean up
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in downloadDocument:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Move a document to a different folder
 */
export async function moveDocument(id: string, folderId: string | null): Promise<{ data: Document | null; error: any }> {
  try {
    console.log('Moving document:', id, 'to folder:', folderId);

    const response = await fetch(`/api/documents/${id}/move`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ folderId }),
    });

    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to move document' };
    }

    return result;
  } catch (error) {
    console.error('Error in moveDocument:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Delete a document
 */
export async function deleteDocument(id: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Deleting document:', id);

    const response = await fetch(`/api/documents/${id}`, {
      method: 'DELETE',
    });

    const result = await response.json();

    if (!response.ok) {
      return { success: false, error: result.error || 'Failed to delete document' };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in deleteDocument:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Network error' };
  }
}


