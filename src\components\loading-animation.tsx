"use client";

import { useEffect, useState } from 'react';

interface LoadingAnimationProps {
  size?: number;
  className?: string;
  text?: string;
}

export function LoadingAnimation({
  size = 120,
  className = "",
  text = "Loading..."
}: LoadingAnimationProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Small delay to prevent flash for very quick loads
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  if (!isVisible) return null;

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div
        className="animate-spin rounded-full border-4 border-gray-200 border-t-blue-600"
        style={{ width: size, height: size }}
      />
      {text && (
        <p className="mt-4 text-sm text-gray-600 font-medium animate-pulse">
          {text}
        </p>
      )}
    </div>
  );
}

// Full screen loading overlay
export function LoadingOverlay({ 
  isVisible = true, 
  text = "Loading..." 
}: { 
  isVisible?: boolean; 
  text?: string; 
}) {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-white bg-opacity-90 z-50 flex items-center justify-center">
      <LoadingAnimation size={150} text={text} />
    </div>
  );
}

// Inline loading for content areas
export function InlineLoading({ 
  text = "Loading...", 
  className = "py-12" 
}: { 
  text?: string; 
  className?: string; 
}) {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <LoadingAnimation size={80} text={text} />
    </div>
  );
}
