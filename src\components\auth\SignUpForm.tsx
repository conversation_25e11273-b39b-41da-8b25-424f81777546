'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import Link from 'next/link';

export function SignUpForm() {
  const router = useRouter();

  // For local authentication, automatically redirect to projects
  useEffect(() => {
    console.log('Local authentication - redirecting to projects');
    router.push('/projects');
  }, [router]);

  return (
    <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-[#0271c3]">Local Authentication</h1>
        <p className="mt-2 text-gray-600">Redirecting to projects...</p>
      </div>

      <div className="p-3 text-sm text-blue-800 bg-blue-100 rounded-md">
        This app is running in local mode. You are automatically authenticated.
      </div>
    </div>
  );
}
