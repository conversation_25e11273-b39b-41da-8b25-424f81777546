// Client-side service that calls API routes

export interface Project {
  id?: string;
  name: string;
  location: string;
  type: string;
  status: string;
  completion?: string;
  description?: string;
  building_consent?: string;
  resource_consent?: string;
  topo_start?: string;
  topo_completed?: string;
  epa?: string;
  works_over?: string;
  works_over_number?: string;
  start_date?: string;
  completion_date?: string;
  estimated_budget?: string;
  actual_cost?: string;
  sale_price?: string;
  lender?: string;
  existing_dwellings?: string;
  new_dwellings?: string;
  client_name?: string;
  project_manager?: string;
  created_at?: string;
  updated_at?: string;
}

export async function createProject(project: Project): Promise<{ data: Project | null; error: any }> {
  try {
    console.log('Creating project:', project);

    const response = await fetch('/api/projects', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(project),
    });

    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to create project' };
    }

    return result;
  } catch (error) {
    console.error('Error in createProject:', error);
    return { data: null, error: error.message || 'Network error' };
  }
}

export async function getProjects(): Promise<{ data: Project[] | null; error: any }> {
  try {
    console.log('Fetching projects from API');

    const response = await fetch('/api/projects');
    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to fetch projects' };
    }

    return result;
  } catch (error) {
    console.error('Error in getProjects:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Network error' };
  }
}

export async function getProjectById(id: string): Promise<{ data: Project | null; error: any }> {
  try {
    console.log(`Fetching project with id: ${id}`);

    const response = await fetch(`/api/projects/${id}`);
    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to fetch project' };
    }

    return result;
  } catch (error) {
    console.error(`Error in getProjectById for ${id}:`, error);
    return { data: null, error: error instanceof Error ? error.message : 'Network error' };
  }
}

export async function updateProject(id: string, project: Partial<Project>): Promise<{ data: Project | null; error: any }> {
  try {
    console.log('Updating project:', { id, project });

    const response = await fetch(`/api/projects/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(project),
    });

    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to update project' };
    }

    return result;
  } catch (error) {
    console.error('Error in updateProject:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Network error' };
  }
}

export async function deleteProject(id: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Deleting project:', id);

    const response = await fetch(`/api/projects/${id}`, {
      method: 'DELETE',
    });

    const result = await response.json();

    if (!response.ok) {
      return { success: false, error: result.error || 'Failed to delete project' };
    }

    return result;
  } catch (error) {
    console.error('Error in deleteProject:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Network error' };
  }
}
