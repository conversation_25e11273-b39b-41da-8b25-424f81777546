# 🚀 Deployment Guide: Supabase + GitHub + Netlify

This guide will help you deploy your project management app with Supabase authentication to Netlify via GitHub.

## 📋 Prerequisites

- GitHub account
- Netlify account
- Supabase account
- Your project code ready for deployment

## 🗄️ Step 1: Set Up Supabase Database

### 1.1 Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project"
3. Create a new project
4. Choose a region close to your users
5. Set a strong database password

### 1.2 Configure Database Schema
1. Go to SQL Editor in your Supabase dashboard
2. Run the following SQL to create the required tables:

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum for user roles
CREATE TYPE user_role AS ENUM ('ADMIN', 'PROJECT_MANAGER', 'VIEWER');

-- Create users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  role user_role DEFAULT 'VIEWER',
  permissions TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  auth_user_id UUID UNIQUE NOT NULL
);

-- Create projects table (update existing or create new)
CREATE TABLE IF NOT EXISTS projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  location TEXT,
  type TEXT,
  status TEXT DEFAULT 'Planning',
  completion TEXT,
  description TEXT,
  building_consent TEXT,
  resource_consent TEXT,
  topo_start TEXT,
  topo_completed TEXT,
  epa TEXT,
  works_over TEXT,
  works_over_number TEXT,
  start_date TEXT,
  completion_date TEXT,
  estimated_budget TEXT,
  actual_cost TEXT,
  sale_price TEXT,
  lender TEXT,
  existing_dwellings TEXT,
  new_dwellings TEXT,
  client_name TEXT,
  project_manager TEXT,
  created_by TEXT,
  assigned_manager_id UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create other tables (documents, photos, folders) if they don't exist
-- [Include your existing table schemas here]

-- Create RLS policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Users can read their own profile
CREATE POLICY "Users can read own profile" ON users
  FOR SELECT USING (auth.uid()::text = auth_user_id::text);

-- Admins can read all users
CREATE POLICY "Admins can read all users" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE auth_user_id::text = auth.uid()::text 
      AND role = 'ADMIN'
    )
  );

-- Projects policies (adjust based on your needs)
CREATE POLICY "Users can read projects" ON projects
  FOR SELECT USING (true); -- Adjust based on your permission system
```

### 1.3 Configure Authentication
1. Go to Authentication > Settings
2. Set Site URL to your domain (e.g., `https://your-app.netlify.app`)
3. Add redirect URLs:
   - `https://your-app.netlify.app/auth/callback`
   - `http://localhost:3000/auth/callback` (for development)
4. Disable "Enable email confirmations" if you want simpler user creation

### 1.4 Create Admin User
1. Go to Authentication > Users
2. Click "Add user"
3. Enter admin email and password
4. After creation, go to SQL Editor and run:

```sql
INSERT INTO users (email, name, role, permissions, auth_user_id, is_active)
VALUES (
  '<EMAIL>',
  'Admin User',
  'ADMIN',
  ARRAY['view_projects', 'create_projects', 'edit_projects', 'delete_projects', 'view_documents', 'upload_documents', 'delete_documents', 'view_photos', 'upload_photos', 'delete_photos', 'manage_users', 'manage_settings'],
  'YOUR_AUTH_USER_ID_FROM_SUPABASE_AUTH_USERS_TABLE',
  true
);
```

## 📁 Step 2: Prepare Your Code for Deployment

### 2.1 Update Environment Variables
Create `.env.local` with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
DATABASE_URL=your_supabase_database_url
NEXTAUTH_SECRET=your_random_secret_key
NEXTAUTH_URL=https://your-app.netlify.app
```

### 2.2 Update Package.json Scripts
Ensure your `package.json` has the correct build scripts:

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "db:generate": "prisma generate",
    "db:push": "prisma db push"
  }
}
```

### 2.3 Install Dependencies
```bash
npm install
```

## 🐙 Step 3: Push to GitHub

### 3.1 Initialize Git Repository
```bash
git init
git add .
git commit -m "Initial commit with Supabase authentication"
```

### 3.2 Create GitHub Repository
1. Go to GitHub and create a new repository
2. Don't initialize with README (since you already have code)
3. Copy the repository URL

### 3.3 Push Code
```bash
git remote add origin https://github.com/yourusername/your-repo-name.git
git branch -M main
git push -u origin main
```

## 🌐 Step 4: Deploy to Netlify

### 4.1 Connect GitHub Repository
1. Log in to Netlify
2. Click "New site from Git"
3. Choose GitHub and authorize Netlify
4. Select your repository

### 4.2 Configure Build Settings
- **Build command**: `npm run build`
- **Publish directory**: `.next`
- **Node version**: 18 or higher

### 4.3 Set Environment Variables
In Netlify dashboard > Site settings > Environment variables, add:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
DATABASE_URL=your_supabase_database_url
NEXTAUTH_SECRET=your_random_secret_key
NEXTAUTH_URL=https://your-app.netlify.app
```

### 4.4 Deploy
1. Click "Deploy site"
2. Wait for build to complete
3. Your app will be available at the generated Netlify URL

## 🔧 Step 5: Post-Deployment Configuration

### 5.1 Update Supabase URLs
1. Go back to Supabase > Authentication > Settings
2. Update Site URL to your Netlify URL
3. Update redirect URLs to include your Netlify domain

### 5.2 Test Authentication
1. Visit your deployed app
2. Try logging in with your admin credentials
3. Test user creation and permissions

### 5.3 Set Up Custom Domain (Optional)
1. In Netlify > Domain settings
2. Add your custom domain
3. Update Supabase settings with new domain

## 🔐 Step 6: Security Checklist

- [ ] Environment variables are set correctly
- [ ] Database RLS policies are enabled
- [ ] Admin user is created and working
- [ ] Authentication redirects are configured
- [ ] HTTPS is enabled (automatic with Netlify)
- [ ] Database backups are configured in Supabase

## 🚨 Troubleshooting

### Common Issues:

1. **Build fails**: Check Node.js version and dependencies
2. **Authentication not working**: Verify environment variables and Supabase settings
3. **Database connection issues**: Check DATABASE_URL format
4. **Permission errors**: Verify RLS policies and user roles

### Debug Steps:
1. Check Netlify build logs
2. Check browser console for errors
3. Verify Supabase logs
4. Test authentication flow step by step

## 📞 Support

If you encounter issues:
1. Check the deployment logs in Netlify
2. Verify all environment variables are set
3. Test locally with production environment variables
4. Check Supabase dashboard for any errors

Your app should now be live with full authentication and user management! 🎉
