"use client";

import { useState, useEffect, useCallback } from 'react';
import { NavSidebar } from '@/components/nav-sidebar';
import { AppHeader } from '@/components/app-header';
import { NewProjectButton } from '@/components/new-project-button';
import { ShareDialog } from '@/components/share-dialog';
import { EnhancedShareDialog } from '@/components/enhanced-share-dialog';
import { FolderTree, FolderItem } from '@/components/folder-tree';
import { FileUpload } from '@/components/file-upload';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, Download, Trash2, Share2, Filter, Upload, FolderPlus } from 'lucide-react';
// Mock document interface
interface Document {
  id: string;
  name: string;
  type: string;
  date: string;
  size: string;
  url?: string;
  file_path?: string;
}
import { toast } from 'sonner';

export default function DocumentsPage() {
  // Document state
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for document management
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [isEnhancedShareDialogOpen, setIsEnhancedShareDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);

  // Folder state
  const [folders, setFolders] = useState<FolderItem[]>([
    {
      id: 'folder-1',
      name: 'Building Permits',
      type: 'folder',
      children: [
        {
          id: 'folder-1-1',
          name: 'Approved',
          type: 'folder',
          parentId: 'folder-1',
          children: []
        },
        {
          id: 'folder-1-2',
          name: 'Pending',
          type: 'folder',
          parentId: 'folder-1',
          children: []
        }
      ]
    },
    {
      id: 'folder-2',
      name: 'Contracts',
      type: 'folder',
      children: []
    },
    {
      id: 'folder-3',
      name: 'Site Plans',
      type: 'folder',
      children: []
    }
  ]);

  // Get documents for sharing
  const documentsForSharing = documents.filter(doc => selectedDocuments.includes(doc.id));

  // Folder management functions
  const createFolder = (parentId: string | null, name: string) => {
    const newFolder: FolderItem = {
      id: `folder-${Date.now()}`,
      name,
      type: 'folder',
      parentId: parentId || undefined,
      children: []
    };

    if (parentId) {
      // Add to parent folder
      setFolders(prev => {
        const updateFolders = (items: FolderItem[]): FolderItem[] => {
          return items.map(item => {
            if (item.id === parentId) {
              return {
                ...item,
                children: [...(item.children || []), newFolder]
              };
            } else if (item.children) {
              return {
                ...item,
                children: updateFolders(item.children)
              };
            }
            return item;
          });
        };
        return updateFolders(prev);
      });
    } else {
      // Add to root
      setFolders(prev => [...prev, newFolder]);
    }

    toast.success(`Folder "${name}" created successfully`);
  };

  const renameItem = (itemId: string, newName: string) => {
    setFolders(prev => {
      const updateItems = (items: FolderItem[]): FolderItem[] => {
        return items.map(item => {
          if (item.id === itemId) {
            return { ...item, name: newName };
          } else if (item.children) {
            return {
              ...item,
              children: updateItems(item.children)
            };
          }
          return item;
        });
      };
      return updateItems(prev);
    });

    toast.success(`Item renamed to "${newName}"`);
  };

  const deleteItem = (itemId: string) => {
    setFolders(prev => {
      const removeItem = (items: FolderItem[]): FolderItem[] => {
        return items.filter(item => {
          if (item.id === itemId) {
            return false;
          } else if (item.children) {
            item.children = removeItem(item.children);
          }
          return true;
        });
      };
      return removeItem(prev);
    });

    toast.success('Item deleted successfully');
  };

  const handleFolderClick = (item: FolderItem) => {
    if (item.type === 'folder') {
      setCurrentFolderId(item.id);
    }
  };

  // Mock function to simulate fetching documents
  const fetchDocuments = useCallback(async () => {
    try {
      setLoading(true);

      // Mock data - replace with actual API call later
      const mockDocuments: Document[] = [
        {
          id: '1',
          name: 'Building Permit Application.pdf',
          type: 'PDF',
          date: '2024-01-15',
          size: '2.5 MB',
          url: '#'
        },
        {
          id: '2',
          name: 'Site Plan.pdf',
          type: 'PDF',
          date: '2024-01-10',
          size: '1.8 MB',
          url: '#'
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      setDocuments(mockDocuments);
      setError(null);
    } catch (err) {
      console.error('Unexpected error:', err);
      setError('An unexpected error occurred. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch documents when the component mounts
  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  // Handle document selection for actions (sharing, etc.)
  const handleDocumentSelect = (docId: string, isChecked: boolean) => {
    if (isChecked) {
      setSelectedDocuments(prev => [...prev, docId]);
    } else {
      setSelectedDocuments(prev => prev.filter(id => id !== docId));
    }
  };

  // Handle select all documents
  const handleSelectAll = (isChecked: boolean) => {
    if (isChecked) {
      setSelectedDocuments(documents.map(doc => doc.id));
    } else {
      setSelectedDocuments([]);
    }
  };

  // Share dialog is opened from the document preview component

  // Mock file upload function
  const handleFileUpload = async (files: File[]) => {
    try {
      setLoading(true);

      // Mock upload process
      const newDocuments: Document[] = files.map((file, index) => ({
        id: `doc-${Date.now()}-${index}`,
        name: file.name,
        type: 'PDF',
        date: new Date().toISOString().split('T')[0],
        size: `${(file.size / (1024 * 1024)).toFixed(1)} MB`,
        url: '#'
      }));

      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      setDocuments(prev => [...prev, ...newDocuments]);
      toast.success(`Successfully uploaded ${newDocuments.length} document${newDocuments.length > 1 ? 's' : ''}`);
    } catch (err) {
      console.error('Error uploading documents:', err);
      toast.error('An error occurred while uploading documents');
    } finally {
      setLoading(false);
    }
  };

  // Filter documents based on search term
  const filteredDocuments = documents.filter(doc =>
    doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="flex flex-col h-screen">
      <AppHeader />
      <div className="flex">
        <NavSidebar />
        <div className="ml-54 mt-[72px] w-full h-screen flex overflow-hidden">
          {/* Folder Tree Sidebar */}
          <div className="w-64 p-4 border-r bg-gray-50 overflow-auto">
            <FolderTree
              items={folders}
              onItemClick={handleFolderClick}
              onCreateFolder={createFolder}
              onRenameItem={renameItem}
              onDeleteItem={deleteItem}
              selectedItemId={currentFolderId || undefined}
            />
          </div>

          {/* Document List Section */}
          <div className="flex-1 p-4 pb-20 overflow-auto">
            <div className="mb-4 flex justify-between items-center">
              <div>
                <h1 className="text-xl font-bold text-blue-700">Documents</h1>
                <p className="text-gray-600 mt-0.5 text-sm">Manage project documents (PDF only)</p>
              </div>
              <div>
                <NewProjectButton />
              </div>
            </div>

            <div className="mb-4 flex flex-col sm:flex-row gap-2 justify-between items-start sm:items-center">
              <div className="relative w-full sm:w-64">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search documents..."
                  className="pl-9 pr-4 py-2 w-full"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex gap-2 w-full sm:w-auto">
                <Button variant="outline" size="sm" className="flex-shrink-0">
                  <Filter className="h-4 w-4 mr-1" />
                  Filter
                </Button>
                <FileUpload
                  onUpload={handleFileUpload}
                  acceptedFileTypes=".pdf"
                  multiple={true}
                  maxFiles={5}
                  maxSize={20}
                  buttonText="Upload PDF"
                  buttonIcon={<Upload className="h-4 w-4 mr-1" />}
                  buttonClassName="bg-blue-600 hover:bg-blue-700 flex-shrink-0 ml-auto sm:ml-0"
                />
              </div>
            </div>

            {selectedDocuments.length > 0 && (
              <div className="mb-4 flex items-center justify-between bg-blue-50 p-3 rounded-md">
                <span className="text-sm font-medium text-blue-700">
                  {selectedDocuments.length} {selectedDocuments.length === 1 ? 'document' : 'documents'} selected
                </span>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-blue-600"
                    onClick={() => setIsEnhancedShareDialogOpen(true)}
                  >
                    <Share2 className="h-4 w-4 mr-1" />
                    Share
                  </Button>
                  <Button variant="outline" size="sm" className="text-blue-600">
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600"
                    onClick={async () => {
                      if (confirm('Are you sure you want to delete the selected documents? This action cannot be undone.')) {
                        try {
                          setLoading(true);

                          // Get the documents to delete
                          const docsToDelete = documents.filter(doc => selectedDocuments.includes(doc.id));

                          // Mock delete process
                          await new Promise(resolve => setTimeout(resolve, 500));

                          // Remove deleted documents from state
                          setDocuments(prev => prev.filter(doc => !selectedDocuments.includes(doc.id)));
                          setSelectedDocuments([]);



                          toast.success(`Successfully deleted ${docsToDelete.length} document(s)`);
                        } catch (err) {
                          console.error('Error deleting documents:', err);
                          toast.error('An error occurred while deleting documents');
                        } finally {
                          setLoading(false);
                        }
                      }
                    }}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                </div>
              </div>
            )}

            <div className="bg-white rounded-lg shadow-md border border-slate-400 overflow-hidden">
              {loading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading documents...</p>
                </div>
              ) : error ? (
                <div className="p-8 text-center">
                  <p className="text-red-600 mb-2">{error}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fetchDocuments()}
                  >
                    Try Again
                  </Button>
                </div>
              ) : documents.length === 0 ? (
                <div className="p-8 text-center">
                  <p className="text-gray-600 mb-2">No documents found</p>
                  <p className="text-sm text-gray-500">Upload documents using the button above</p>
                </div>
              ) : (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 py-3 text-left">
                        <div className="flex items-center">
                          <Checkbox
                            id="select-all"
                            checked={selectedDocuments.length === documents.length}
                            onCheckedChange={handleSelectAll}
                            className="mr-2"
                          />
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Name
                          </span>
                        </div>
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Size
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredDocuments.map((doc) => (
                      <tr
                        key={doc.id}
                        className="hover:bg-gray-50"
                      >
                        <td className="px-3 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <Checkbox
                              checked={selectedDocuments.includes(doc.id)}
                              onCheckedChange={(checked) => {
                                handleDocumentSelect(doc.id, checked as boolean);
                              }}
                              onClick={(e) => e.stopPropagation()}
                              className="mr-3"
                            />
                            <DocumentIcon className="text-gray-500 mr-3" />
                            <span className="text-sm font-medium text-gray-900">{doc.name}</span>
                          </div>
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap">
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            {doc.type}
                          </span>
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                          {doc.date}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                          {doc.size}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-blue-600 hover:text-blue-800"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (doc.url) {
                                const link = document.createElement('a');
                                link.href = doc.url;
                                link.download = doc.name;
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                                toast.success("Downloading document...");
                              } else {
                                toast.error("Unable to download document. The file URL is not available.");
                              }
                            }}
                          >
                            <Download className="h-4 w-4 mr-1" />
                            Download
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>


        </div>
      </div>

      {/* Share Dialogs */}
      <ShareDialog
        isOpen={isShareDialogOpen}
        onClose={() => setIsShareDialogOpen(false)}
        documents={documentsForSharing}
      />

      <EnhancedShareDialog
        isOpen={isEnhancedShareDialogOpen}
        onClose={() => setIsEnhancedShareDialogOpen(false)}
        documents={documentsForSharing}
      />
    </div>
  );
}

// Document icon component
function DocumentIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="18"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
      <polyline points="14 2 14 8 20 8"></polyline>
    </svg>
  );
}


