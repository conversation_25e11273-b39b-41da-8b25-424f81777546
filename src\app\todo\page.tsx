'use client';

import { NavSidebar } from '@/components/nav-sidebar';
import { AppHeader } from '@/components/app-header';
import { useState } from 'react';
import { NewProjectButton } from '@/components/new-project-button';

interface Todo {
  id: number;
  task: string;
  completed: boolean;
  dueDate: string;
  priority: 'High' | 'Medium' | 'Low';
  assignedTo: string;
}

export default function TodoPage() {
  // Sample todo data
  const [todos, setTodos] = useState<Todo[]>([
    { id: 1, task: 'Submit building permit application', completed: true, dueDate: '2025-01-10', priority: 'High', assignedTo: '<PERSON>' },
    { id: 2, task: 'Finalize floor plans with architect', completed: true, dueDate: '2025-01-15', priority: 'High', assignedTo: '<PERSON>' },
    { id: 3, task: 'Order construction materials', completed: false, dueDate: '2025-01-25', priority: 'Medium', assignedTo: '<PERSON>' },
    { id: 4, task: 'Schedule initial site inspection', completed: false, dueDate: '2025-01-30', priority: 'Medium', assignedTo: '<PERSON>' },
    { id: 5, task: 'Hire subcontractors for electrical work', completed: false, dueDate: '2025-02-05', priority: 'High', assignedTo: 'Sarah <PERSON>' },
  ]);

  const [newTask, setNewTask] = useState('');

  const toggleComplete = (id: number) => {
    setTodos(
      todos.map((todo) =>
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      )
    );
  };

  const addTask = () => {
    if (newTask.trim()) {
      const newTodo: Todo = {
        id: Date.now(),
        task: newTask,
        completed: false,
        dueDate: new Date().toISOString().split('T')[0],
        priority: 'Medium',
        assignedTo: 'Unassigned',
      };
      setTodos([...todos, newTodo]);
      setNewTask('');
    }
  };

  const deleteTask = (id: number) => {
    setTodos(todos.filter((todo) => todo.id !== id));
  };

  return (
    <div className="flex flex-col h-screen">
      <AppHeader />
      <div className="flex">
        <NavSidebar />
        <div className="ml-54 mt-[72px] p-6 pb-20 w-full h-screen overflow-y-auto">
          <div className="mb-4 flex justify-between items-center">
            <div>
              <h1 className="text-xl font-bold">To-Do List</h1>
              <p className="text-gray-600 mt-0.5 text-sm">Manage project tasks and assignments</p>
            </div>
            <div>
              <NewProjectButton />
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow-md border border-slate-400">
          <div className="flex mb-4">
            <input
              type="text"
              placeholder="Add a new task..."
              className="flex-1 p-2 border border-slate-400 rounded-l-md text-sm"
              value={newTask}
              onChange={(e) => setNewTask(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && addTask()}
            />
            <button
              className="bg-blue-600 text-white px-3 py-2 rounded-r-md text-sm"
              onClick={addTask}
            >
              Add Task
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10">
                    Status
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Task
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Due Date
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Assigned To
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {todos.map((todo) => (
                  <tr key={todo.id} className={todo.completed ? 'bg-gray-50' : ''}>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={todo.completed}
                        onChange={() => toggleComplete(todo.id)}
                        className="h-3.5 w-3.5 text-blue-600 rounded"
                      />
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span className={`text-xs font-medium ${todo.completed ? 'text-gray-400 line-through' : 'text-gray-900'}`}>
                        {todo.task}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-xs text-gray-500">
                      {todo.dueDate}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span className={`px-1.5 py-0.5 inline-flex text-xs leading-4 font-semibold rounded-md ${
                        todo.priority === 'High'
                          ? 'bg-red-100 text-red-800'
                          : todo.priority === 'Medium'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {todo.priority}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-xs text-gray-500">
                      {todo.assignedTo}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-xs font-medium">
                      <button
                        className="text-red-600 hover:text-red-900"
                        onClick={() => deleteTask(todo.id)}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          </div>
        </div>
      </div>
    </div>
  );
}


