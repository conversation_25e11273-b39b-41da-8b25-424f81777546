"use client";

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Upload, X, File, Image, FileText, Check } from 'lucide-react';

interface FileUploadProps {
  onUpload: (files: File[]) => void;
  acceptedFileTypes?: string;
  multiple?: boolean;
  maxFiles?: number;
  maxSize?: number; // in MB
  buttonText?: string;
  buttonIcon?: React.ReactNode;
  buttonVariant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  buttonSize?: "default" | "sm" | "lg" | "icon";
  buttonClassName?: string;
}

export function FileUpload({
  onUpload,
  acceptedFileTypes = "*",
  multiple = false,
  maxFiles = 10,
  maxSize = 10, // 10MB default
  buttonText = "Upload",
  buttonIcon = <Upload className="h-4 w-4 mr-1" />,
  buttonVariant = "default",
  buttonSize = "default",
  buttonClassName = "",
}: FileUploadProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [errors, setErrors] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleButtonClick = () => {
    setIsOpen(true);
    setSelectedFiles([]);
    setErrors([]);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      validateAndAddFiles(Array.from(e.target.files));
    }
  };

  const validateAndAddFiles = (files: File[]) => {
    const newErrors: string[] = [];
    const validFiles: File[] = [];

    // Check if adding these files would exceed the max number of files
    if (multiple && selectedFiles.length + files.length > maxFiles) {
      newErrors.push(`You can only upload a maximum of ${maxFiles} files.`);
      return;
    }

    files.forEach(file => {
      // Check file size
      if (file.size > maxSize * 1024 * 1024) {
        newErrors.push(`${file.name} exceeds the maximum file size of ${maxSize}MB.`);
        return;
      }

      // Check file type if specified
      if (acceptedFileTypes !== "*") {
        const fileType = file.type;
        const fileName = file.name.toLowerCase();
        const acceptedTypes = acceptedFileTypes.split(',');

        if (!acceptedTypes.some(type => {
          // Handle wildcards like "image/*"
          if (type.endsWith('/*')) {
            const category = type.split('/')[0];
            return fileType.startsWith(category + '/');
          }
          // Handle MIME types (e.g., application/pdf)
          if (type.includes('/')) {
            return type === fileType;
          }
          // Handle file extensions (e.g., .pdf)
          if (type.startsWith('.')) {
            return fileName.endsWith(type);
          }
          return type === fileType;
        })) {
          console.log(`File type validation failed for ${file.name}:`, {
            fileType: file.type,
            fileName: file.name,
            acceptedTypes: acceptedTypes
          });
          newErrors.push(`${file.name} is not an accepted file type. File type: ${file.type || 'unknown'}`);
          return;
        }
      }

      validFiles.push(file);
    });

    if (validFiles.length > 0) {
      if (multiple) {
        setSelectedFiles(prev => [...prev, ...validFiles]);
      } else {
        setSelectedFiles(validFiles.slice(0, 1));
      }
    }

    setErrors(newErrors);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files) {
      validateAndAddFiles(Array.from(e.dataTransfer.files));
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleUpload = () => {
    if (selectedFiles.length > 0) {
      onUpload(selectedFiles);
      setIsOpen(false);
    }
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="h-5 w-5 text-blue-500" />;
    } else if (file.type.includes('pdf')) {
      return <FileText className="h-5 w-5 text-red-500" />;
    } else if (file.type.includes('word') || file.type.includes('document')) {
      return <FileText className="h-5 w-5 text-blue-500" />;
    } else if (file.type.includes('sheet') || file.type.includes('excel') || file.type.includes('csv')) {
      return <FileText className="h-5 w-5 text-green-500" />;
    } else {
      return <File className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <>
      <Button
        type="button"
        variant={buttonVariant}
        size={buttonSize}
        className={buttonClassName}
        onClick={handleButtonClick}
      >
        {buttonIcon}
        {buttonText}
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Upload Files</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center ${
                isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <Upload className="h-10 w-10 text-gray-400 mx-auto mb-3" />
              <p className="text-sm text-gray-600 mb-1">
                Drag and drop files here, or
              </p>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
              >
                Browse Files
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileInputChange}
                accept={acceptedFileTypes}
                multiple={multiple}
              />
              <p className="text-xs text-gray-500 mt-2">
                {multiple
                  ? `You can upload up to ${maxFiles} files (max ${maxSize}MB each)`
                  : `Maximum file size: ${maxSize}MB`
                }
              </p>
            </div>

            {errors.length > 0 && (
              <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm">
                <p className="font-medium mb-1">The following errors occurred:</p>
                <ul className="list-disc pl-5 space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
                <p className="text-xs mt-2">
                  Accepted file types: {acceptedFileTypes}
                </p>
              </div>
            )}

            {selectedFiles.length > 0 && (
              <div>
                <Label className="text-sm font-medium mb-2 block">Selected Files</Label>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {selectedFiles.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between bg-gray-50 p-2 rounded-md"
                    >
                      <div className="flex items-center">
                        {getFileIcon(file)}
                        <span className="ml-2 text-sm truncate max-w-[200px]">{file.name}</span>
                        <span className="ml-2 text-xs text-gray-500">
                          {(file.size / (1024 * 1024)).toFixed(2)} MB
                        </span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 rounded-full"
                        onClick={() => removeFile(index)}
                      >
                        <X className="h-4 w-4 text-gray-500" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <DialogFooter className="sm:justify-between">
            <Button
              type="button"
              variant="ghost"
              onClick={() => setIsOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleUpload}
              disabled={selectedFiles.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Check className="h-4 w-4 mr-1" />
              Upload {selectedFiles.length > 0 ? `(${selectedFiles.length})` : ''}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
