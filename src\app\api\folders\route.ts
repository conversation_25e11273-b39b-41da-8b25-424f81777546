import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/folders?projectId=xxx&type=documents|photos
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const type = searchParams.get('type');
    
    if (!projectId) {
      return NextResponse.json({ data: null, error: 'Project ID is required' }, { status: 400 });
    }

    if (!type || !['documents', 'photos'].includes(type)) {
      return NextResponse.json({ data: null, error: 'Type must be documents or photos' }, { status: 400 });
    }

    console.log('API: Fetching folders for project:', projectId, 'type:', type);

    const folders = await prisma.folder.findMany({
      where: {
        project_id: projectId,
        type: type,
      },
      include: {
        children: {
          include: {
            children: true, // Support 2 levels of nesting for now
          }
        },
        documents: type === 'documents',
        photos: type === 'photos',
      },
      orderBy: {
        created_at: 'asc',
      },
    });

    // Build hierarchical structure
    const rootFolders = folders.filter(folder => !folder.parent_id);
    const buildHierarchy = (folder: any) => ({
      id: folder.id,
      name: folder.name,
      type: 'folder' as const,
      parentId: folder.parent_id,
      children: folder.children?.map(buildHierarchy) || [],
      created_at: folder.created_at.toISOString(),
      updated_at: folder.updated_at.toISOString(),
    });

    const foldersData = rootFolders.map(buildHierarchy);

    return NextResponse.json({ data: foldersData, error: null });
  } catch (error) {
    console.error('API Error in GET /api/folders:', error);
    return NextResponse.json({ data: null, error: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}

// POST /api/folders
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, projectId, parentId, type } = body;
    
    if (!name || !projectId || !type) {
      return NextResponse.json({ data: null, error: 'Name, project ID, and type are required' }, { status: 400 });
    }

    if (!['documents', 'photos'].includes(type)) {
      return NextResponse.json({ data: null, error: 'Type must be documents or photos' }, { status: 400 });
    }

    console.log('API: Creating folder:', name, 'for project:', projectId);

    // Check if parent folder exists if parentId is provided
    if (parentId) {
      const parentFolder = await prisma.folder.findUnique({
        where: { id: parentId }
      });
      
      if (!parentFolder) {
        return NextResponse.json({ data: null, error: 'Parent folder not found' }, { status: 404 });
      }
      
      if (parentFolder.project_id !== projectId) {
        return NextResponse.json({ data: null, error: 'Parent folder belongs to different project' }, { status: 400 });
      }
    }

    const folder = await prisma.folder.create({
      data: {
        name,
        project_id: projectId,
        parent_id: parentId || null,
        type,
      },
    });

    const folderData = {
      id: folder.id,
      name: folder.name,
      type: 'folder' as const,
      parentId: folder.parent_id,
      project_id: folder.project_id,
      created_at: folder.created_at.toISOString(),
      updated_at: folder.updated_at.toISOString(),
    };

    return NextResponse.json({ data: folderData, error: null });
  } catch (error) {
    console.error('API Error in POST /api/folders:', error);
    return NextResponse.json({ data: null, error: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}
