import { createClient } from '@/lib/supabase/client'
import { createClient as createServerClient } from '@/lib/supabase/server'

export interface User {
  id: string
  email: string
  name?: string
  role: 'ADMIN' | 'PROJECT_MANAGER' | 'VIEWER'
  permissions: string[]
  is_active: boolean
  auth_user_id: string
}

export interface LoginCredentials {
  email: string
  password: string
}

// Client-side auth functions
export async function signIn(credentials: LoginCredentials) {
  const supabase = createClient()
  
  const { data, error } = await supabase.auth.signInWithPassword({
    email: credentials.email,
    password: credentials.password,
  })

  if (error) {
    return { user: null, error: error.message }
  }

  // Get user profile from our database
  const userProfile = await getUserProfile(data.user.id)
  
  if (!userProfile) {
    await supabase.auth.signOut()
    return { user: null, error: 'User profile not found. Please contact an administrator.' }
  }

  if (!userProfile.is_active) {
    await supabase.auth.signOut()
    return { user: null, error: 'Your account has been deactivated. Please contact an administrator.' }
  }

  return { user: userProfile, error: null }
}

export async function signOut() {
  const supabase = createClient()
  const { error } = await supabase.auth.signOut()
  return { error }
}

export async function getCurrentUser(): Promise<User | null> {
  const supabase = createClient()
  
  const { data: { user: authUser } } = await supabase.auth.getUser()
  
  if (!authUser) {
    return null
  }

  return await getUserProfile(authUser.id)
}

// Server-side auth functions
export async function getCurrentUserServer(): Promise<User | null> {
  const supabase = await createServerClient()
  
  const { data: { user: authUser } } = await supabase.auth.getUser()
  
  if (!authUser) {
    return null
  }

  return await getUserProfile(authUser.id)
}

// Get user profile from our database
async function getUserProfile(authUserId: string): Promise<User | null> {
  try {
    const response = await fetch('/api/users/profile', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ auth_user_id: authUserId }),
    })

    if (!response.ok) {
      return null
    }

    const { data } = await response.json()
    return data
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return null
  }
}

// Permission checking functions
export function hasPermission(user: User | null, permission: string): boolean {
  if (!user || !user.is_active) return false
  
  // Admin has all permissions
  if (user.role === 'ADMIN') return true
  
  // Check specific permissions
  return user.permissions.includes(permission)
}

export function canAccessProjects(user: User | null): boolean {
  return hasPermission(user, 'view_projects')
}

export function canCreateProjects(user: User | null): boolean {
  return hasPermission(user, 'create_projects')
}

export function canEditProjects(user: User | null): boolean {
  return hasPermission(user, 'edit_projects')
}

export function canDeleteProjects(user: User | null): boolean {
  return hasPermission(user, 'delete_projects')
}

export function canAccessDocuments(user: User | null): boolean {
  return hasPermission(user, 'view_documents')
}

export function canAccessPhotos(user: User | null): boolean {
  return hasPermission(user, 'view_photos')
}

export function canManageUsers(user: User | null): boolean {
  return hasPermission(user, 'manage_users')
}

// Default permissions for each role
export const DEFAULT_PERMISSIONS = {
  ADMIN: [
    'view_projects',
    'create_projects',
    'edit_projects',
    'delete_projects',
    'view_documents',
    'upload_documents',
    'delete_documents',
    'view_photos',
    'upload_photos',
    'delete_photos',
    'manage_users',
    'manage_settings'
  ],
  PROJECT_MANAGER: [
    'view_projects',
    'create_projects',
    'edit_projects',
    'view_documents',
    'upload_documents',
    'view_photos',
    'upload_photos'
  ],
  VIEWER: [
    'view_projects',
    'view_documents',
    'view_photos'
  ]
}
