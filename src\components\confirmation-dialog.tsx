'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Trash2, Save, X } from 'lucide-react';

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive' | 'warning';
  icon?: React.ReactNode;
}

export function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  icon
}: ConfirmationDialogProps) {
  if (!isOpen) return null;

  const getVariantStyles = () => {
    switch (variant) {
      case 'destructive':
        return {
          iconBg: 'bg-red-100',
          iconColor: 'text-red-600',
          confirmButton: 'bg-red-600 hover:bg-red-700 text-white',
          defaultIcon: <Trash2 className="h-6 w-6" />
        };
      case 'warning':
        return {
          iconBg: 'bg-amber-100',
          iconColor: 'text-amber-600',
          confirmButton: 'bg-amber-600 hover:bg-amber-700 text-white',
          defaultIcon: <AlertTriangle className="h-6 w-6" />
        };
      default:
        return {
          iconBg: 'bg-blue-100',
          iconColor: 'text-blue-600',
          confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white',
          defaultIcon: <Save className="h-6 w-6" />
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 animate-fade-in-scale">
        <div className="flex items-start mb-4">
          <div className={`${styles.iconBg} p-2 rounded-full mr-4`}>
            <div className={styles.iconColor}>
              {icon || styles.defaultIcon}
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <p className="text-gray-600 mt-1">{message}</p>
          </div>
        </div>
        <div className="flex justify-end gap-3 mt-6">
          <Button
            variant="outline"
            className="border-gray-300"
            onClick={onClose}
          >
            <X className="h-4 w-4 mr-1" />
            {cancelText}
          </Button>
          <Button
            className={styles.confirmButton}
            onClick={onConfirm}
          >
            {variant === 'destructive' && <Trash2 className="h-4 w-4 mr-1" />}
            {variant === 'warning' && <AlertTriangle className="h-4 w-4 mr-1" />}
            {variant === 'default' && <Save className="h-4 w-4 mr-1" />}
            {confirmText}
          </Button>
        </div>
      </div>
    </div>
  );
}
