import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";

import { Toaster } from "@/components/ui/sonner";
import { UnsavedChangesProvider } from "@/contexts/unsaved-changes-context";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-sans",
});

const montserrat = Montserrat({
  subsets: ["latin"],
  weight: ["500", "600", "700", "800"],
  variable: "--font-heading",
});

export const metadata: Metadata = {
  title: "AH Projects - Project Management",
  description: "Project management application for construction projects",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body suppressHydrationWarning className={`${poppins.variable} ${montserrat.variable} font-sans antialiased bg-[#f9fdff] overflow-hidden`}>
        <UnsavedChangesProvider>
          {/* The children will include A<PERSON>Header and NavSidebar in each page */}
          {children}
          <Toaster />
        </UnsavedChangesProvider>
      </body>
    </html>
  );
}
