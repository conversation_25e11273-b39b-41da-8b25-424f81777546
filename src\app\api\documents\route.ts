import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import fs from 'fs';
import path from 'path';

// GET /api/documents?projectId=xxx
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json({ data: null, error: 'Project ID is required' }, { status: 400 });
    }

    console.log('API: Fetching documents for project:', projectId);

    const documents = await prisma.document.findMany({
      where: {
        project_id: projectId,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    const documentsData = documents.map((doc: any) => ({
      id: doc.id,
      name: doc.name,
      file_path: doc.file_path,
      file_size: doc.file_size,
      mime_type: doc.mime_type,
      folder_id: doc.folder_id,
      project_id: doc.project_id,
      created_at: doc.created_at.toISOString(),
      updated_at: doc.updated_at.toISOString(),
    }));

    return NextResponse.json({ data: documentsData, error: null });
  } catch (error) {
    console.error('API Error in GET /api/documents:', error);
    return NextResponse.json({ data: null, error: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}

// POST /api/documents
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const projectId = formData.get('projectId') as string;
    const folderId = formData.get('folderId') as string;
    
    if (!file) {
      return NextResponse.json({ data: null, error: 'No file provided' }, { status: 400 });
    }

    if (!projectId) {
      return NextResponse.json({ data: null, error: 'Project ID is required' }, { status: 400 });
    }

    console.log('API: Creating document record:', file.name);

    // Generate a unique file path
    const fileId = crypto.randomUUID();
    const fileExt = file.name.split('.').pop();
    const filePath = `${fileId}.${fileExt}`;

    // Save the actual file to the uploads directory

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'documents');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // Save the file
    const buffer = Buffer.from(await file.arrayBuffer());
    const fullFilePath = path.join(uploadsDir, filePath);
    fs.writeFileSync(fullFilePath, buffer);

    // Create document record in database
    const document = await prisma.document.create({
      data: {
        name: file.name,
        file_path: filePath,
        file_size: file.size,
        mime_type: file.type,
        project_id: projectId,
        folder_id: folderId || null,
      },
    });

    const documentData = {
      id: document.id,
      name: document.name,
      file_path: document.file_path,
      file_size: document.file_size,
      mime_type: document.mime_type,
      folder_id: document.folder_id,
      project_id: document.project_id,
      created_at: document.created_at.toISOString(),
      updated_at: document.updated_at.toISOString(),
    };

    return NextResponse.json({ data: documentData, error: null });
  } catch (error) {
    console.error('API Error in POST /api/documents:', error);
    return NextResponse.json({ data: null, error: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}

// DELETE /api/documents?id=xxx
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Document ID is required' }, { status: 400 });
    }

    console.log('API: Deleting document:', id);

    // Get document from database
    const document = await prisma.document.findUnique({
      where: { id }
    });

    if (!document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Delete file from filesystem
    const filePath = path.join(process.cwd(), 'public', 'uploads', 'documents', document.file_path);

    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (fileError) {
      console.warn('Warning: Could not delete file from disk:', fileError);
      // Continue with database deletion even if file deletion fails
    }

    // Delete document record from database
    await prisma.document.delete({
      where: { id }
    });

    return NextResponse.json({ success: true, error: null });
  } catch (error) {
    console.error('API Error in DELETE /api/documents:', error);
    return NextResponse.json({ success: false, error: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}
