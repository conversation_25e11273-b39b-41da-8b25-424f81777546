'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckCircle2 } from 'lucide-react';
import { useUnsavedChanges } from '@/hooks/use-unsaved-changes';

export default function TestSavePage() {
  const [hasChanges, setHasChanges] = useState(false);
  const [projectName, setProjectName] = useState('Test Project');
  const [projectStatus, setProjectStatus] = useState('Planning');

  const handleFieldChange = (field: string, value: string) => {
    console.log(`Field changed: ${field} = ${value}`);
    console.log('hasChanges is now:', true);
    
    if (field === 'name') {
      setProjectName(value);
    } else if (field === 'status') {
      setProjectStatus(value);
    }
    
    setHasChanges(true);
  };

  const handleSave = async () => {
    console.log('Saving...');
    // Simulate save
    await new Promise(resolve => setTimeout(resolve, 1000));
    setHasChanges(false);
    console.log('Saved successfully');
  };

  // Use unsaved changes hook for navigation protection
  const { checkUnsavedChanges, saveAndNavigate } = useUnsavedChanges({
    hasUnsavedChanges: hasChanges,
    onSave: handleSave,
    message: 'You have unsaved changes. Are you sure you want to leave?'
  });

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Test Save Functionality</h1>
      
      <div className="space-y-4 max-w-md">
        <div>
          <label className="block text-sm font-medium mb-2">Project Name</label>
          <Input
            value={projectName}
            onChange={(e) => handleFieldChange('name', e.target.value)}
            placeholder="Enter project name"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">Status</label>
          <Select
            value={projectStatus}
            onValueChange={(value) => handleFieldChange('status', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Planning">Planning</SelectItem>
              <SelectItem value="In Progress">In Progress</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
              <SelectItem value="On Hold">On Hold</SelectItem>
              <SelectItem value="For Sale">For Sale</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Debug info */}
      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h3 className="font-bold">Debug Info:</h3>
        <p>hasChanges: {hasChanges.toString()}</p>
        <p>projectName: {projectName}</p>
        <p>projectStatus: {projectStatus}</p>
      </div>

      {/* Floating Save Button */}
      {hasChanges && (
        <div className="fixed bottom-8 right-8 z-50 animate-fade-in">
          <Button
            className="bg-blue-600 hover:bg-blue-700 shadow-lg px-6 py-6 rounded-full flex items-center gap-2 transition-all duration-300 hover:scale-105"
            onClick={handleSave}
          >
            <CheckCircle2 className="h-5 w-5" />
            <span className="font-medium">Save Changes</span>
          </Button>
        </div>
      )}
    </div>
  );
}
