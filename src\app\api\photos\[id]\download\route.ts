import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { readFile } from 'fs/promises';
import { join } from 'path';

// GET /api/photos/[id]/download
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log('API: Downloading photo:', id);
    
    // Get photo from database
    const photo = await prisma.photo.findUnique({
      where: { id }
    });

    if (!photo) {
      return NextResponse.json({ error: 'Photo not found' }, { status: 404 });
    }

    // Construct file path
    const filePath = join(process.cwd(), 'public', 'uploads', 'photos', photo.file_path);
    
    try {
      // Read the file
      const fileBuffer = await readFile(filePath);
      
      // Set appropriate headers for download
      const headers = new Headers();
      headers.set('Content-Type', photo.mime_type || 'image/jpeg');
      headers.set('Content-Disposition', `attachment; filename="${photo.title}.${photo.file_path.split('.').pop()}"`);
      headers.set('Content-Length', fileBuffer.length.toString());
      
      return new NextResponse(fileBuffer, {
        status: 200,
        headers,
      });
    } catch (fileError) {
      console.error('Error reading file:', fileError);
      return NextResponse.json({ error: 'File not found on disk' }, { status: 404 });
    }
  } catch (error) {
    console.error('API Error in GET /api/photos/[id]/download:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}
