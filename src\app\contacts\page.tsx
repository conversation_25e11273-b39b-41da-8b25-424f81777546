'use client';

import { useState } from 'react';
import { NavSidebar } from '@/components/nav-sidebar';
import { AppHeader } from '@/components/app-header';
import { NewProjectButton } from '@/components/new-project-button';
import { FileUpload } from '@/components/file-upload';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Search, Plus, Mail, Phone, Building, MapPin, Upload } from 'lucide-react';

export default function ContactsPage() {
  // Sample contacts data
  const [contacts, setContacts] = useState([
    {
      id: 1,
      name: '<PERSON>',
      role: 'Project Manager',
      company: 'ABC Construction',
      email: '<EMAIL>',
      phone: '(*************',
      address: '123 Main St, Anytown, CA 94321'
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'Architect',
      company: 'Modern Design Studio',
      email: '<EMAIL>',
      phone: '(*************',
      address: '456 Oak Ave, Somewhere, CA 94322'
    },
    {
      id: 3,
      name: '<PERSON>',
      role: 'Structural Engineer',
      company: '<PERSON> Engineering',
      email: '<EMAIL>',
      phone: '(*************',
      address: '789 Pine Rd, Elsewhere, CA 94323'
    },
    {
      id: 4,
      name: 'Emily Rodriguez',
      role: 'Interior Designer',
      company: 'Interior Visions',
      email: '<EMAIL>',
      phone: '(*************',
      address: '101 Cedar Blvd, Nowhere, CA 94324'
    },
    {
      id: 5,
      name: 'David Wilson',
      role: 'Contractor',
      company: 'Wilson Building Co.',
      email: '<EMAIL>',
      phone: '(*************',
      address: '202 Elm St, Anywhere, CA 94325'
    },
  ]);

  const [searchTerm, setSearchTerm] = useState('');

  // Handle contact import
  const handleContactImport = (files: File[]) => {
    // In a real app, this would parse CSV/vCard files
    // For this demo, we'll just create dummy contacts

    const newContacts = files.map((file, index) => {
      // Generate a unique ID for the new contact
      const newId = Math.max(...contacts.map(contact => contact.id)) + index + 1;

      // Create a dummy contact based on the file name
      const nameParts = file.name.split('.');
      const name = nameParts[0].replace(/_/g, ' ');

      return {
        id: newId,
        name: name,
        role: 'Imported Contact',
        company: 'Imported Company',
        email: `${name.toLowerCase().replace(/\s/g, '.')}@example.com`,
        phone: `(555) ${Math.floor(100 + Math.random() * 900)}-${Math.floor(1000 + Math.random() * 9000)}`,
        address: '123 Import Street, Anytown, CA 94321'
      };
    });

    setContacts([...contacts, ...newContacts]);

    // Show a success message
    alert(`Successfully imported ${files.length} contact${files.length > 1 ? 's' : ''}`);
  };

  // Filter contacts based on search term
  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="flex flex-col h-screen">
      <AppHeader />
      <div className="flex">
        <NavSidebar />
        <div className="ml-54 mt-[72px] p-6 pb-20 w-full h-screen overflow-y-auto">
          <div className="mb-4 flex justify-between items-center">
            <div>
              <h1 className="text-xl font-bold text-blue-700">Contacts</h1>
              <p className="text-gray-600 mt-0.5 text-sm">Manage project contacts and team members</p>
            </div>
            <div>
              <NewProjectButton />
            </div>
          </div>

          <div className="mb-4 flex flex-col sm:flex-row gap-3 justify-between items-start sm:items-center">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search contacts..."
                className="pl-9 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <FileUpload
                onUpload={handleContactImport}
                acceptedFileTypes=".csv,.vcf"
                multiple={true}
                maxFiles={10}
                maxSize={5}
                buttonText="Import Contacts"
                buttonIcon={<Upload className="h-4 w-4 mr-1" />}
                buttonVariant="outline"
                buttonClassName="text-blue-600 border-blue-600"
              />
              <Button className="bg-blue-600 hover:bg-blue-700 flex-shrink-0">
                <Plus className="h-4 w-4 mr-1" />
                Add Contact
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredContacts.map((contact) => (
              <Card key={contact.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <CardContent className="p-0">
                  <div className="bg-blue-50 p-3 border-b">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center">
                        <div className="bg-blue-600 text-white rounded-lg w-8 h-8 flex items-center justify-center mr-2.5 text-xs font-bold">
                          {contact.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div>
                          <h3 className="font-semibold text-blue-800 text-sm">{contact.name}</h3>
                          <p className="text-xs text-gray-600">{contact.role}</p>
                        </div>
                      </div>
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0 rounded-md">
                          <Mail className="h-3 w-3 text-blue-600" />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0 rounded-md">
                          <Phone className="h-3 w-3 text-blue-600" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="p-3">
                    <div className="space-y-1.5">
                      <div className="flex items-start">
                        <Building className="h-3.5 w-3.5 text-gray-500 mt-0.5 mr-1.5" />
                        <span className="text-xs">{contact.company}</span>
                      </div>
                      <div className="flex items-start">
                        <Mail className="h-3.5 w-3.5 text-gray-500 mt-0.5 mr-1.5" />
                        <span className="text-xs">{contact.email}</span>
                      </div>
                      <div className="flex items-start">
                        <Phone className="h-3.5 w-3.5 text-gray-500 mt-0.5 mr-1.5" />
                        <span className="text-xs">{contact.phone}</span>
                      </div>
                      <div className="flex items-start">
                        <MapPin className="h-3.5 w-3.5 text-gray-500 mt-0.5 mr-1.5" />
                        <span className="text-xs">{contact.address}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
