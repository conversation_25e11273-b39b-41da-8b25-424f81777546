'use client';

import { NavSidebar } from '@/components/nav-sidebar';
import { AppHeader } from '@/components/app-header';

export default function ContactsPage() {
  // This feature is disabled in this version
  return (
    <div className="min-h-screen bg-gray-50">
      <AppHeader />
      <NavSidebar />
      <div className="ml-54 mt-[72px] p-6 pb-20 w-full h-screen overflow-y-auto">
        <div className="flex items-center justify-center h-full">
          <div className="text-center bg-white p-8 rounded-lg shadow-sm border border-gray-200 max-w-md">
            <div className="mb-4">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Feature Disabled</h3>
            <p className="text-gray-600 mb-4">
              Contacts is currently disabled in this version of the app.
            </p>
            <p className="text-sm text-gray-500">
              This feature will be available in the next version.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

