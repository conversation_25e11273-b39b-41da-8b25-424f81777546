'use client';

import Link from 'next/link';
import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { getProjects, Project } from '@/services/project-service';

// CSS for animations
const headerStyles = {
  '@keyframes fadeIn': {
    '0%': { opacity: 0, transform: 'translateY(10px)' },
    '100%': { opacity: 1, transform: 'translateY(0)' }
  },
  '.animate-fade-in': {
    animation: 'fadeIn 0.3s ease-out forwards'
  }
};

export function AppHeader({
  hideSearch = false,
  showProjectSearch = false,
  projectSearchTerm = '',
  onProjectSearchChange = (term: string) => {},
  onToggleFilters = () => {},
  showFiltersButton = false,
  filtersActive = false
}: {
  hideSearch?: boolean,
  showProjectSearch?: boolean,
  projectSearchTerm?: string,
  onProjectSearchChange?: (term: string) => void,
  onToggleFilters?: () => void,
  showFiltersButton?: boolean,
  filtersActive?: boolean
}) {
  const [searchTerm, setSearchTerm] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [allProjects, setAllProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    type: ''
  });
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const router = useRouter();
  const searchRef = useRef<HTMLDivElement>(null);

  // Fetch projects from Supabase - optimized to reduce unnecessary fetches
  useEffect(() => {
    async function fetchProjects() {
      try {
        setIsLoading(true);
        const { data, error } = await getProjects();

        if (error) {
          // Reduced logging to improve performance
          console.error('Error fetching projects for search');
        } else if (data) {
          setAllProjects(data);
          setFilteredProjects(data);
        }
      } catch (err) {
        console.error('Error fetching projects');
      } finally {
        setIsLoading(false);
      }
    }

    fetchProjects();

    // Removed interval and focus event listeners to improve performance
  }, []);

  // Close suggestions when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Filter projects based on search term and filters
  useEffect(() => {
    let results = allProjects;

    // Apply search term filter
    if (searchTerm) {
      results = results.filter(project =>
        (project.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        (project.location?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        (project.type?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        (project.description?.toLowerCase() || '').includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (filters.status) {
      results = results.filter(project => project.status === filters.status);
    }

    // Apply type filter
    if (filters.type) {
      results = results.filter(project => project.type === filters.type);
    }

    setFilteredProjects(results);
  }, [searchTerm, filters, allProjects]);

  // Get unique project types for filter dropdown
  const projectTypes = [...new Set(allProjects.map(project => project.type).filter(Boolean))];

  return (
    <header className="bg-[#0271c3] text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50">
      <div className="container mx-auto flex justify-between items-center">
        {/* Logo */}
        <div className="flex items-center pl-4">
          <Link href="/" className="flex items-center">
            <img
              src="/Logo 500x500 px (3).png"
              alt="AH Projects"
              className="h-11 w-auto"
            />
          </Link>
        </div>

        {/* Global Search Bar - Only shown when hideSearch is false */}
        {!hideSearch ? (
          <div className="flex-1 max-w-md mx-4" ref={searchRef}>
            <div className="relative">
              <div className="flex">
                <input
                  type="text"
                  placeholder="Search projects..."
                  className="w-full py-2 pl-10 pr-4 rounded-l-md text-gray-800 bg-white focus:outline-none focus:ring-2 focus:ring-[#0271c3]"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setShowSuggestions(true);
                  }}
                  onFocus={() => setShowSuggestions(true)}
                />
              <button
                className={`px-3 rounded-r-md flex items-center transition-all duration-200 ${
                  showFilters
                    ? "bg-blue-700 text-white hover:bg-blue-800"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
                onClick={() => setShowFilters(!showFilters)}
                aria-label="Toggle filters"
              >
                <FilterIcon className={`mr-1 ${showFilters ? 'text-white' : 'text-blue-600'}`} />
                <span className="font-medium">Filter</span>
                {filters.status || filters.type ? (
                  <span className="ml-1 bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {(filters.status ? 1 : 0) + (filters.type ? 1 : 0)}
                  </span>
                ) : null}
              </button>
            </div>
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />

            {/* Auto-suggest dropdown */}
            {showSuggestions && (
              <div className="absolute z-10 w-full bg-white mt-1 rounded-md shadow-lg max-h-60 overflow-auto">
                {isLoading ? (
                  <div className="px-4 py-3 text-gray-500 flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading projects...
                  </div>
                ) : filteredProjects.length > 0 ? (
                  filteredProjects.map(project => (
                    <Link
                      key={project.id}
                      href={`/projects/${project.id}`}
                      className="block px-4 py-2 hover:bg-gray-100 text-gray-800"
                      onClick={() => setShowSuggestions(false)}
                    >
                      <div className="font-medium">{project.name}</div>
                      <div className="text-sm text-gray-500 flex items-center justify-between">
                        <span>{project.location}</span>
                        <span className={`text-xs px-2 py-0.5 rounded-full ${
                          project.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                          project.status === 'Planning' ? 'bg-yellow-100 text-yellow-800' :
                          project.status === 'Completed' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {project.status}
                        </span>
                      </div>
                    </Link>
                  ))
                ) : searchTerm ? (
                  <div className="px-4 py-3 text-gray-500">
                    <div className="font-medium">No projects found</div>
                    <div className="text-sm">Try a different search term or create a new project</div>
                  </div>
                ) : (
                  <div className="px-4 py-3 text-gray-500">
                    <div className="font-medium">Recent Projects</div>
                    {allProjects.slice(0, 5).map(project => (
                      <Link
                        key={project.id}
                        href={`/projects/${project.id}`}
                        className="block px-2 py-1.5 mt-1 hover:bg-gray-100 text-gray-800 rounded"
                        onClick={() => setShowSuggestions(false)}
                      >
                        <div className="font-medium text-sm">{project.name}</div>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Advanced filters dropdown */}
            {showFilters && (
              <div className="absolute z-10 w-full bg-white mt-1 rounded-md shadow-lg p-5 border border-gray-200 animate-fade-in">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-gray-800 text-lg">Filters</h3>
                  <button
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                    onClick={() => setShowFilters(false)}
                    aria-label="Close filters"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <div className="flex flex-wrap gap-2">
                    <button
                      className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                        filters.status === ''
                          ? 'bg-blue-100 text-blue-800 border-2 border-blue-300'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border-2 border-transparent'
                      }`}
                      onClick={() => setFilters({...filters, status: ''})}
                    >
                      All
                    </button>
                    <button
                      className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                        filters.status === 'In Progress'
                          ? 'bg-blue-100 text-blue-800 border-2 border-blue-300'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border-2 border-transparent'
                      }`}
                      onClick={() => setFilters({...filters, status: 'In Progress'})}
                    >
                      In Progress
                    </button>
                    <button
                      className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                        filters.status === 'Planning'
                          ? 'bg-blue-100 text-blue-800 border-2 border-blue-300'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border-2 border-transparent'
                      }`}
                      onClick={() => setFilters({...filters, status: 'Planning'})}
                    >
                      Planning
                    </button>
                    <button
                      className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                        filters.status === 'Completed'
                          ? 'bg-blue-100 text-blue-800 border-2 border-blue-300'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border-2 border-transparent'
                      }`}
                      onClick={() => setFilters({...filters, status: 'Completed'})}
                    >
                      Completed
                    </button>
                  </div>
                </div>

                <div className="mb-5">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Project Type</label>
                  <div className="flex flex-wrap gap-2">
                    <button
                      className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                        filters.type === ''
                          ? 'bg-blue-100 text-blue-800 border-2 border-blue-300'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border-2 border-transparent'
                      }`}
                      onClick={() => setFilters({...filters, type: ''})}
                    >
                      All Types
                    </button>
                    {projectTypes.map(type => (
                      <button
                        key={type}
                        className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                          filters.type === type
                            ? 'bg-blue-100 text-blue-800 border-2 border-blue-300'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border-2 border-transparent'
                        }`}
                        onClick={() => setFilters({...filters, type})}
                      >
                        {type}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="flex justify-between pt-3 border-t border-gray-200">
                  <button
                    className="px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 transition-colors font-medium flex items-center"
                    onClick={() => {
                      setFilters({ status: '', type: '' });
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                      <path d="M3 3h18v18H3z"></path>
                      <path d="M21 3l-9 9"></path>
                      <path d="M12 12l-9 9"></path>
                    </svg>
                    Reset All
                  </button>
                  <button
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium flex items-center"
                    onClick={() => setShowFilters(false)}
                  >
                    Apply Filters
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1">
                      <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
        ) : showProjectSearch ? (
          <div className="flex-1 max-w-md mx-4">
            <div className="relative">
              <div className="flex">
                <div className="relative flex-1">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                    <SearchIcon className="h-4 w-4" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search projects, address, client..."
                    className="w-full py-2 pl-10 pr-4 rounded-md border border-gray-300 bg-white text-gray-800 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-400 transition-all duration-200 text-sm"
                    value={projectSearchTerm}
                    onChange={(e) => onProjectSearchChange(e.target.value)}
                  />
                  {projectSearchTerm && (
                    <button
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      onClick={() => onProjectSearchChange('')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  )}
                </div>

                {showFiltersButton && (
                  <button
                    className={`ml-2 px-3 py-2 rounded-md flex items-center transition-all duration-200 text-sm ${
                      filtersActive
                        ? "bg-blue-600 text-white hover:bg-blue-700 border border-blue-700"
                        : "bg-gray-50 text-gray-700 hover:bg-gray-100 border border-gray-300"
                    }`}
                    onClick={onToggleFilters}
                  >
                    <FilterIcon className="h-4 w-4 mr-1.5" />
                    <span className="font-medium">Filters</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 max-w-md mx-4"></div>
        )}

        {/* Home Button */}
        <div className="relative group">
          <button
            onClick={() => {
              router.push('/');
            }}
            className="flex items-center text-white bg-[#0271c3] px-3 py-1.5 rounded-md hover:bg-[#0271c3]/80 hover:scale-105 active:scale-95 active:bg-[#025a9e] transition-all duration-200 mr-4 cursor-pointer shadow-sm hover:shadow-md"
            aria-label="Go to home page"
          >
            <HomeIcon className="mr-2" />
            Home
          </button>

          {/* Tooltip */}
          <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
            Go to home page
          </div>
        </div>
      </div>
    </header>
  );
}

// Search icon component
function SearchIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="18"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <circle cx="11" cy="11" r="8"></circle>
      <path d="m21 21-4.3-4.3"></path>
    </svg>
  );
}

// Filter icon component
function FilterIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
    </svg>
  );
}

// Home icon component
function HomeIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="18"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
      <polyline points="9 22 9 12 15 12 15 22"></polyline>
    </svg>
  );
}
