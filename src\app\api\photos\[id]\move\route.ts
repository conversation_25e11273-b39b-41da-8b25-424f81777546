import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// PUT /api/photos/[id]/move
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { folderId } = body;
    
    console.log('API: Moving photo:', id, 'to folder:', folderId);
    
    // Check if photo exists
    const existingPhoto = await prisma.photo.findUnique({
      where: { id }
    });

    if (!existingPhoto) {
      return NextResponse.json({ data: null, error: 'Photo not found' }, { status: 404 });
    }

    // If folderId is provided, check if folder exists and belongs to the same project
    if (folderId) {
      const folder = await prisma.folder.findUnique({
        where: { id: folderId }
      });
      
      if (!folder) {
        return NextResponse.json({ data: null, error: 'Folder not found' }, { status: 404 });
      }
      
      if (folder.project_id !== existingPhoto.project_id) {
        return NextResponse.json({ data: null, error: 'Folder belongs to different project' }, { status: 400 });
      }
      
      if (folder.type !== 'photos') {
        return NextResponse.json({ data: null, error: 'Folder is not a photos folder' }, { status: 400 });
      }
    }

    // Update the photo's folder
    const photo = await prisma.photo.update({
      where: { id },
      data: { folder_id: folderId || null },
    });

    const photoData = {
      id: photo.id,
      title: photo.title,
      file_path: photo.file_path,
      file_size: photo.file_size,
      mime_type: photo.mime_type,
      folder_id: photo.folder_id,
      project_id: photo.project_id,
      thumbnail: `/uploads/photos/${photo.file_path}`,
      date: photo.created_at.toISOString().split('T')[0],
      created_at: photo.created_at.toISOString(),
      updated_at: photo.updated_at.toISOString(),
    };

    return NextResponse.json({ data: photoData, error: null });
  } catch (error) {
    console.error('API Error in PUT /api/photos/[id]/move:', error);
    return NextResponse.json({ 
      data: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}
