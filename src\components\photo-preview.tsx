"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download, Share2, X, ZoomIn, ZoomOut } from 'lucide-react';
import { toast } from 'sonner';

interface Photo {
  id: string;
  title: string;
  date: string;
  thumbnail: string;
  file_path?: string;
}

interface PhotoPreviewProps {
  photo: Photo | null;
  onShare?: (photo: Photo) => void;
  onClose?: () => void;
}

export function PhotoPreview({ photo, onShare, onClose }: PhotoPreviewProps) {
  const [zoom, setZoom] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);

  if (!photo) {
    return (
      <div className="flex flex-col items-center justify-center h-full bg-[#f9fdff] rounded-lg border-2 border-dashed border-gray-300 p-8">
        <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center mb-4">
          <svg
            className="w-8 h-8 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-600">No photo selected</h3>
        <p className="text-sm text-gray-500 mt-2 text-center">
          Select a photo from the list to preview it here
        </p>
      </div>
    );
  }

  const handleDownload = () => {
    const imageUrl = photo.file_path ? `/uploads/photos/${photo.file_path}` : photo.thumbnail;
    if (imageUrl) {
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = photo.title;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success("Downloading photo...");
    } else {
      toast.error("Unable to download photo. The image URL is not available.");
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.25, 0.25));
  };

  const resetZoom = () => {
    setZoom(1);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{photo.title}</h3>
          <p className="text-sm text-gray-500">{photo.date}</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleZoomOut}
            disabled={zoom <= 0.25}
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          <span className="text-sm text-gray-600 min-w-[60px] text-center">
            {Math.round(zoom * 100)}%
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleZoomIn}
            disabled={zoom >= 3}
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={resetZoom}
          >
            Reset
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
          >
            <Download className="h-4 w-4 mr-1" />
            Download
          </Button>
          {onShare && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onShare(photo)}
            >
              <Share2 className="h-4 w-4 mr-1" />
              Share
            </Button>
          )}
          {onClose && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Image Preview */}
      <div className="flex-1 overflow-auto bg-gray-100 p-4">
        <div className="flex items-center justify-center h-full">
          <div 
            className="relative max-w-full max-h-full"
            style={{ transform: `scale(${zoom})` }}
          >
            <img
              src={photo.file_path ? `/uploads/photos/${photo.file_path}` : photo.thumbnail}
              alt={photo.title}
              className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
              onError={(e) => {
                console.error('Error loading image:', photo.file_path || photo.thumbnail);
                const imgElement = e.currentTarget as HTMLImageElement;
                imgElement.src = `https://placehold.co/400x300/e2e8f0/1e40af?text=${encodeURIComponent(photo.title)}`;
              }}
              onClick={toggleFullscreen}
              style={{ cursor: 'pointer' }}
            />
          </div>
        </div>
      </div>

      {/* Fullscreen Modal */}
      {isFullscreen && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
          <div className="relative max-w-full max-h-full p-4">
            <Button
              variant="outline"
              size="sm"
              className="absolute top-4 right-4 z-10 bg-white"
              onClick={toggleFullscreen}
            >
              <X className="h-4 w-4" />
            </Button>
            <img
              src={photo.file_path ? `/uploads/photos/${photo.file_path}` : photo.thumbnail}
              alt={photo.title}
              className="max-w-full max-h-full object-contain"
              onError={(e) => {
                console.error('Error loading image:', photo.file_path || photo.thumbnail);
                const imgElement = e.currentTarget as HTMLImageElement;
                imgElement.src = `https://placehold.co/800x600/e2e8f0/1e40af?text=${encodeURIComponent(photo.title)}`;
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
