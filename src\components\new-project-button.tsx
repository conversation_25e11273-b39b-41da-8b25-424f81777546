"use client";

import { useState } from 'react';
import { Plus } from 'lucide-react';
import { AddProjectForm } from './add-project-form';

interface NewProjectButtonProps {
  onProjectCreated?: () => void;
}

export function NewProjectButton({ onProjectCreated }: NewProjectButtonProps) {
  const [isFormOpen, setIsFormOpen] = useState(false);

  return (
    <>
      <button
        className="bg-[#0271c3] text-white px-5 py-2.5 rounded-lg flex items-center hover:bg-[#0271c3]/90 transition-colors shadow-md cursor-pointer"
        onClick={() => setIsFormOpen(true)}
      >
        <Plus className="h-4 w-4 mr-2" />
        New Project
      </button>

      <AddProjectForm
        open={isFormOpen}
        onOpenChange={setIsFormOpen}
        onProjectCreated={onProjectCreated}
      />
    </>
  );
}
