import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/projects/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log(`API: Fetching project with id: ${id}`);
    
    const project = await prisma.project.findUnique({
      where: {
        id: id,
      },
    });

    if (!project) {
      return NextResponse.json({ data: null, error: 'Project not found' }, { status: 404 });
    }

    const projectData = {
      ...project,
      created_at: project.created_at.toISOString(),
      updated_at: project.updated_at.toISOString(),
    };

    return NextResponse.json({ data: projectData, error: null });
  } catch (error) {
    const { id } = await params;
    console.error(`API Error in GET /api/projects/${id}:`, error);
    return NextResponse.json({
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// PUT /api/projects/[id]
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const project = await request.json();
    console.log('API: Updating project:', { id, project });
    
    const updatedProject = await prisma.project.update({
      where: {
        id: id,
      },
      data: {
        ...(project.name && { name: project.name }),
        ...(project.location !== undefined && { location: project.location }),
        ...(project.type !== undefined && { type: project.type }),
        ...(project.status !== undefined && { status: project.status }),
        ...(project.completion !== undefined && { completion: project.completion }),
        ...(project.description !== undefined && { description: project.description }),
        ...(project.building_consent !== undefined && { building_consent: project.building_consent }),
        ...(project.resource_consent !== undefined && { resource_consent: project.resource_consent }),
        ...(project.topo_start !== undefined && { topo_start: project.topo_start }),
        ...(project.topo_completed !== undefined && { topo_completed: project.topo_completed }),
        ...(project.epa !== undefined && { epa: project.epa }),
        ...(project.works_over !== undefined && { works_over: project.works_over }),
        ...(project.works_over_number !== undefined && { works_over_number: project.works_over_number }),
        ...(project.start_date !== undefined && { start_date: project.start_date }),
        ...(project.completion_date !== undefined && { completion_date: project.completion_date }),
        ...(project.estimated_budget !== undefined && { estimated_budget: project.estimated_budget }),
        ...(project.actual_cost !== undefined && { actual_cost: project.actual_cost }),
        ...(project.sale_price !== undefined && { sale_price: project.sale_price }),
        ...(project.lender !== undefined && { lender: project.lender }),
        ...(project.existing_dwellings !== undefined && { existing_dwellings: project.existing_dwellings }),
        ...(project.new_dwellings !== undefined && { new_dwellings: project.new_dwellings }),
        ...(project.client_name !== undefined && { client_name: project.client_name }),
        ...(project.project_manager !== undefined && { project_manager: project.project_manager }),
      },
    });

    const projectData = {
      ...updatedProject,
      created_at: updatedProject.created_at.toISOString(),
      updated_at: updatedProject.updated_at.toISOString(),
    };

    return NextResponse.json({ data: projectData, error: null });
  } catch (error) {
    const { id } = await params;
    console.error(`API Error in PUT /api/projects/${id}:`, error);
    return NextResponse.json({
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// DELETE /api/projects/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log('API: Deleting project:', id);

    // Check if project exists first
    const existingProject = await prisma.project.findUnique({
      where: { id },
      include: {
        documents: true,
        photos: true,
        folders: true,
      }
    });

    if (!existingProject) {
      return NextResponse.json({ success: false, error: 'Project not found' }, { status: 404 });
    }

    console.log(`Project has ${existingProject.documents.length} documents, ${existingProject.photos.length} photos, ${existingProject.folders.length} folders`);

    // Delete the project (cascade should handle related records)
    await prisma.project.delete({
      where: {
        id: id,
      },
    });

    console.log('Project deleted successfully');
    return NextResponse.json({ success: true, error: null });
  } catch (error) {
    console.error(`API Error in DELETE /api/projects/${(await params).id}:`, error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
