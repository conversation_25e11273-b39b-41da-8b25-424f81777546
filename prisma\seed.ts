import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create sample projects
  const projects = [
    {
      name: 'Dreadon Road Development',
      location: '33b Dreadon Road, Manurewa',
      type: 'Standalone',
      status: 'In Progress',
      description: 'A modern standalone home development project located in Manurewa.',
      completion: 'December 15, 2025',
      sale_price: '$750,000',
      client_name: '<PERSON>',
      project_manager: '<PERSON>',
      start_date: '2023-01-15',
      completion_date: '2024-12-31',
    },
    {
      name: 'Oakridge Apartments',
      location: '45 Oakridge Blvd, Auckland',
      type: 'Apartments',
      status: 'For Sale',
      description: 'A luxury apartment complex with 24 units featuring modern designs and premium amenities.',
      completion: 'June 30, 2026',
      sale_price: '$1,200,000',
      client_name: '<PERSON>',
      project_manager: '<PERSON>',
      start_date: '2023-03-01',
      completion_date: '2026-06-30',
    },
    {
      name: 'Sunset Terraces',
      location: '12 Sunset Drive, Hamilton',
      type: 'Terraced House',
      status: 'For Sale',
      description: 'Beautiful terraced houses with modern finishes and private gardens.',
      completion: 'March 20, 2025',
      sale_price: '$650,000',
      client_name: '<PERSON>',
      project_manager: 'Lisa Chen',
      start_date: '2023-06-01',
      completion_date: '2025-03-20',
    },
    {
      name: 'Commercial Plaza',
      location: '88 Queen Street, Auckland',
      type: 'Other',
      status: 'Completed',
      description: 'Mixed-use commercial development with retail spaces and office units.',
      completion: 'January 10, 2024',
      sale_price: '$2,500,000',
    },
    {
      name: 'Riverside Homes',
      location: '25 River Road, Tauranga',
      type: 'Standalone',
      status: 'Planning',
      description: 'Eco-friendly standalone homes with river views and sustainable design features.',
      completion: 'September 15, 2025',
      sale_price: '$850,000',
    },
  ]

  for (const project of projects) {
    await prisma.project.create({
      data: project,
    })
  }

  console.log('✅ Database seeded successfully!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
