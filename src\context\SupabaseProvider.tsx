'use client';

import { createContext, useContext, useEffect, useState } from 'react';

type User = {
  id: string;
  email: string;
  name?: string;
};

type Session = {
  user: User;
  expires_at: number;
};

type AuthContextType = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
};

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  isLoading: true,
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simple local authentication - always authenticated for private use
    console.log('Setting up local authentication');

    // Create a local user
    const localUser: User = {
      id: 'local-user',
      email: '<EMAIL>',
      name: 'Local User',
    };

    // Create a local session
    const localSession: Session = {
      user: localUser,
      expires_at: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
    };

    // Set the local user and session
    setUser(localUser);
    setSession(localSession);
    setIsLoading(false);

    return () => {};
  }, []);

  const value = {
    user,
    session,
    isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => useContext(AuthContext);

// Keep the old export for backward compatibility
export const useSupabase = useAuth;
export const SupabaseProvider = AuthProvider;
